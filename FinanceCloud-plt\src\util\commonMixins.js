/**
 * FinanceCloud 通用混入 - commonMixin
 * 
 * 包含项目中常用的工具方法和业务逻辑，主要功能包括：
 * 1. 路由跳转管理（解决keep-alive缓存问题）
 * 2. 数据字典获取
 * 3. 科目代码格式化
 * 4. 期间管理
 * 5. 库存配置获取
 * 6. 通用工具方法
 * 
 * 重要说明 - keep-alive缓存问题解决方案：
 * 由于项目启用了keep-alive来提升性能，但这会导致路由跳转后页面数据不刷新的问题。
 * 本文件提供了jump()和jumpPJ()函数来解决此问题：
 * 
 * 解决方案架构：
 * 1. main.js中重写了Vue Router的push/replace方法，自动调用jump函数
 * 2. jump函数通过/empty页面作为中转，清除目标路由缓存
 * 3. jumpPJ函数专门处理项目管理模块，使用/projectEmpty作为中转
 * 4. empty.vue页面负责清除缓存并重新跳转到目标页面
 * 
 * 使用效果：
 * - 开发者无需修改现有代码，this.$router.push()会自动使用jump函数
 * - 确保每次路由跳转都能获取最新数据
 * - 保持了代码的向后兼容性
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import {getStore} from "@/util/store";
import store from '../store'
import {formatSubjectCode,getTextParams,getQueryString,getCurrentDateTime,deepClone} from "@/util/util";
import XEUtils from 'xe-utils'
import request from '@/router/axios'
const commonMixin = {
  data(){
    return {
      elSize: 'mini', // 统一管理 element 组件 size
      contentStyleObj: {
        height: '600px'
      }
    }
  },
  created() {
    this.getHight()
    window.addEventListener('resize', this.getHight)
  },
  destroyed() {
    window.removeEventListener('resize', this.getHight)
  },
  methods: {
    // 获取可用屏幕高度
    getHight() {
      this.contentStyleObj.height = window.innerHeight - 240
    },

    /**
     * 通用文件导出方法
     * @param {Object} config 配置对象
     * @param {Function} config.api 必填，API请求方法
     * @param {Object} config.params 请求参数
     * @param {String} config.fileName 输出文件名
     * @param {String} config.fileType 文件类型（pdf/excel）
     * @param {String} config.successMsg 成功提示信息
     * @param {String} config.loadingText 加载提示文字
     * @returns {Promise}
     */
    commonExportFile(config) {
      return new Promise((resolve, reject) => {
        const {
          api,
          params = {},
          fileName = 'file',
          fileExt = 'xlsx',
          fileNameNeedTimeSpan = false,
          fileType = 'pdf',
          successMsg = '文件生成成功',
          loadingText = '文件生成中，请稍候...'
        } = config;

        // 提前创建新标签页
        const newTab = window.open('', '_blank');
        if (!newTab || newTab.closed) {
          this.$message.error('请允许弹窗权限');
          return reject(new Error('popup_blocked'));
        }

        // 显示加载提示
        newTab.document.write(`
      <div style="text-align:center;padding:50px;">
        <h3 style="color:#409EFF;">${loadingText}</h3>
        <div class="loading-spinner"></div>
      </div>
      <style>
        .loading-spinner {
          width: 40px;
          height: 40px;
          margin: 20px auto;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #409EFF;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `);

        api(params)
          .then(res => {
            // 统一处理 headers
            const contentType = res.headers ?
              (res.headers.get ? res.headers.get('content-type') : res.headers['content-type']) :
              '';

            // 修改后的处理逻辑
            if (contentType && contentType.includes('application/json')) {
              const reader = new FileReader();

              // 创建新Promise并返回它
              return new Promise((resolve, reject) => {
                reader.onload = () => {
                  try {
                    const errorData = JSON.parse(reader.result);
                    reject(new Error(errorData.message || '服务器返回错误'));
                  } catch (e) {
                    reject(new Error('响应解析失败'));
                  }
                };
                reader.onerror = () => {
                  reject(new Error('文件读取失败'));
                };
                reader.readAsText(res.data);
              }).catch(error => {
                if (newTab && !newTab.closed) newTab.close();
                // this.$message.error(error.message);
                return Promise.reject(error); // 关键！继续抛出错误
              });
            }

            // 校验文件类型
            const validTypes = {
              pdf: 'application/pdf',
              excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };

            if (!validTypes[fileType].includes(res.data.type)) {
              // 有的個別接口沒有設置格式 兼容處理 忽略錯誤
              // throw new Error('无效的文件格式');
            }

            // 创建下载链接
            // 防止后台没有何止流类型
            let  resType= validTypes[fileType];
            const blob = new Blob([res.data], { type: resType });
            const url = URL.createObjectURL(blob);

            // 不同文件类型处理
            if (fileType === 'pdf') {
              newTab.location.href = url;
              newTab.focus();
            } else {
              //downloadFileName 规则： 判断有后缀则不需要加后缀， 判断是否要加时间戳
              let downloadFileName = fileName;
              if (fileExt && !fileName.includes('.')) {
                downloadFileName = `${fileName}.${fileExt}`;
              }
              if (fileNameNeedTimeSpan) {
                downloadFileName = `${downloadFileName}_${getCurrentDateTime()}`;
              }
              console.log(downloadFileName);
              const link = document.createElement('a');
              link.href = url;
              link.download = downloadFileName;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              if (!newTab.closed) newTab.close();
            }

        // 自动清理资源 // excel 需要清理
        if (fileType === 'excel') {
          setTimeout(() => {
            URL.revokeObjectURL(url);
            if (!newTab.closed) newTab.close();
          }, 5000);
        }


        this.$message.success(successMsg);
        resolve();
      })
      .catch(error => {
        console.error('[Export Error]', error);
        if (newTab && !newTab.closed) newTab.close();

        const errorMap = {
          'popup_blocked': '请允许浏览器弹窗权限',
          '无效的文件格式': '服务器返回了无效的文件格式',
          '无可用打印数据': '没有符合条件的数据'
        };

        this.$message.error(
          errorMap[error.message] ||
          `文件生成失败：${error.message}`
        );
        reject(error);
      });
  });
}

  ,



    /**
     * 路由跳转函数 - 解决keep-alive缓存导致的数据不刷新问题
     * 
     * 工作原理：
     * 1. 当flag=false时，通过先跳转到空白页面(/empty)，再跳转到目标页面的方式
     * 2. 在空白页面中会清除目标路由的缓存，然后重新跳转到目标页面
     * 3. 这样可以强制刷新目标页面的数据，解决keep-alive缓存问题
     * 
     * @param {Object} route - 路由对象
     * @param {string} route.path - 目标路由路径，如：'/erp/mrp/MrpPlanList'
     * @param {Object} [route.query] - 路由查询参数，如：{id: 123, type: 'edit'}
     * @param {Object} [route.params] - 路由参数
     * @param {boolean} [flag=false] - 是否仅做普通路由跳转
     *   - true: 直接使用$router.push()跳转，不清除缓存
     *   - false: 通过空白页面中转，清除缓存后跳转（默认行为）
     * 
     * @example
     * // 基础用法 - 跳转并刷新数据
     * this.jump({path: '/erp/mrp/MrpPlanList'});
     * 
     * // 带查询参数的跳转
     * this.jump({
     *   path: '/erp/mrp/MrpPlanDetail',
     *   query: {id: 123, mode: 'edit'}
     * });
     * 
     * // 仅做普通跳转，不清除缓存
     * this.jump({path: '/erp/mrp/MrpPlanList'}, true);
     * 
     * // 替代原有的$router.push()调用
     * // 原来：this.$router.push({path: '/some/path', query: {id: 1}});
     * // 现在：this.jump({path: '/some/path', query: {id: 1}});
     */
    jump(location, flag = false) {
      // 如果flag为true，直接进行普通路由跳转，不清除缓存
      if(flag){
        this.$router.push(location);
        return;
      }
      
      // 判断location类型，统一处理
      let routeInfo = {};
      
      if (typeof location === 'string') {
        // 字符串路径
        routeInfo = { path: location };
      } else if (location && typeof location === 'object') {
        // 对象路径，安全提取路由信息，避免循环引用
        routeInfo = this.extractRouteInfo(location);
      } else {
        // 无效参数，使用原始push
        console.warn('jump: 无效的路由参数，使用原始push方法', location);
        this.$router.push(location);
        return;
      }
      
      // 将目标路由信息序列化，通过查询参数传递给空白页面
      let routeJson = JSON.stringify(routeInfo);
      
      // 先跳转到空白页面，空白页面会清除目标路由缓存后再跳转到目标页面
      this.$router.push({ path: '/empty', query: {q: routeJson}});
    },

    /**
     * 安全提取路由信息，避免循环引用问题
     * @param {Object} location - 路由位置对象
     * @returns {Object} 安全的路由信息对象
     */
    extractRouteInfo(location) {
      const safeRouteInfo = {};
      
      // 提取 path
      if (location.path !== undefined) {
        safeRouteInfo.path = location.path;
      }
      
      // 提取 name
      if (location.name !== undefined) {
        safeRouteInfo.name = location.name;
      }
      
      // 提取 query，确保是纯对象
      if (location.query && typeof location.query === 'object') {
        safeRouteInfo.query = {};
        for (const key in location.query) {
          if (location.query.hasOwnProperty(key)) {
            const value = location.query[key];
            // 只保留基本类型值，避免循环引用
            if (value !== null && 
                (typeof value === 'string' || 
                 typeof value === 'number' || 
                 typeof value === 'boolean' ||
                 Array.isArray(value))) {
              safeRouteInfo.query[key] = value;
            }
          }
        }
      }
      
      // 提取 params，确保是纯对象
      if (location.params && typeof location.params === 'object') {
        safeRouteInfo.params = {};
        for (const key in location.params) {
          if (location.params.hasOwnProperty(key)) {
            const value = location.params[key];
            // 只保留基本类型值，避免循环引用
            if (value !== null && 
                (typeof value === 'string' || 
                 typeof value === 'number' || 
                 typeof value === 'boolean' ||
                 Array.isArray(value))) {
              safeRouteInfo.params[key] = value;
            }
          }
        }
      }
      
      // 提取 hash
      if (location.hash !== undefined) {
        safeRouteInfo.hash = location.hash;
      }
      
      return safeRouteInfo;
    },

    /**
     * 项目管理模块专用路由跳转函数 - 解决keep-alive缓存导致的数据不刷新问题
     * 
     * 参数格式与 this.$router.push 完全一致，专门用于项目管理模块
     * 
     * @param {string|Object} location - 路由位置，与 $router.push 参数完全一致
     * @param {boolean} [flag=false] - 是否不清除缓存
     *   - true: 不清除缓存，直接跳转
     *   - false: 清除缓存后跳转（默认行为）
     * 
     * @example
     * // 项目管理模块内的路由跳转
     * this.jumpPJ('/projectManager/taskManager/taskList');
     * this.jumpPJ({path: '/projectManager/taskManager/taskList'});
     * 
     * // 带参数的项目路由跳转
     * this.jumpPJ({
     *   path: '/projectManager/projectGroups/index',
     *   query: {projectId: 456}
     * });
     * 
     * // 不清除缓存的项目路由跳转
     * this.jumpPJ('/projectManager/dashboard', true);
     */
    jumpPJ(location, flag = false) {
        // 如果flag为true，直接进行普通路由跳转，不清除缓存
        if(flag){
          this.$router.push(location);
          return;
        }
        
        // 判断location类型，统一处理
        let routeInfo = {};
        
        if (typeof location === 'string') {
          // 字符串路径
          routeInfo = { path: location };
        } else if (location && typeof location === 'object') {
          // 对象路径，安全提取路由信息，避免循环引用
          routeInfo = this.extractRouteInfo(location);
        } else {
          // 无效参数，使用原始push
          console.warn('jumpPJ: 无效的路由参数，使用原始push方法', location);
          this.$router.push(location);
          return;
        }
        
        // 将目标路由信息序列化
        let routeJson = JSON.stringify(routeInfo);
        
        // 跳转到项目管理专用的空白页面
        this.$router.push({ path: '/projectEmpty', query: {q: routeJson}});
    },



    closeTab(){
      this.$store.commit('DEL_TAG', this.tag);
    },

    getCurrentDateTime(format){
      return getCurrentDateTime(format);
    },
    getCurrentDate(){
      return getCurrentDateTime('yyyy-MM-dd');
    },

    formatTime(timestr){
      try{
        if(timestr){
          return timestr.substr(0,10)
        }else{
          return ''
        }
      }catch(e){

      }

    },

    formatAmounts(cellValue ,length ){
      return XEUtils.commafy(cellValue, {digits: length || 2})
    },

    formatPrice(cellValue){
      let length = 2
      try {
        let data = Number(cellValue).toString().split(".")
        if(data.length > 1){
          length = data[1].length > 2 ? data[1].length : 2
        }
      } catch (error) {}
      return XEUtils.commafy(cellValue, {digits: length || 2})
    },

    getQueryString(name){
     return  getQueryString(this.$route.fullPath,name) ;
    },

    requestApi(obj){
      if(!obj.method){
        obj.method='post';
      }
      return request(obj)
    },

    /**
     * 获取字典数据
     */
    getDict(key){
      return request({
        url: `/admin/dict/type/${key}`,
        method: 'get'
      })
    },

     // 获取当前账套ID
     getCurrentAccountId() {
      return this.getCurrentAccountInfo().accountId
     },
    // 获取当前账套
     getCurrentAccountInfo() {
       return getStore({ name: 'currentAccount'});
    },

    /***
     * 科目维护后需要更新
     * @returns {Promise<unknown>|Promise<any>}
     */
    reloadSubjectList() {
      // 重新更新并获取数据
      return this.$store.dispatch("LoadSubject");
    },

    /***
     * 科目维护后需要更新
     * @returns {Promise<unknown>|Promise<any>}
     */
     getSubjectList() {
       let subjectList = getStore({name: 'subjectList'});
       if(subjectList!=null&&subjectList.length>0){
         return new Promise((resolve, reject) => {
            try{
              resolve(subjectList)
            }catch(e){
              reject(e);
            }
         })
       }
       else{
         // 重新更新并获取数据
         return this.$store.dispatch("LoadSubject");
       }
    },
    /**
     * 获取一级科目
     * @returns
     */
    getOneLevelSubjectList() {
        let subjectList = getStore({name: 'subjectList'});
        if(subjectList!=null&&subjectList.length>0){
          return new Promise((resolve, reject) => {
             try{
                subjectList = subjectList.filter(item => {
                  return item.level == 1
                })
               resolve(subjectList)
             }catch(e){
               reject(e);
             }
          })
        }
        else{
            return new Promise((resolve, reject) => {
                try{
                    this.$store.dispatch("LoadSubject").then(Response=>{
                        let _subjectList = Response;
                        _subjectList = _subjectList.filter(item => {
                            return item.subjectLevel == 1
                          })
                    })
                  resolve(_subjectList)
                }catch(e){
                  reject(e);
                }
             })
        }
     },


    /***
     * 结账后续更新当前账期
     * @returns {Promise<unknown>|Promise<any>}
     */
    getCurrentPeriod() {
      let currentPeriod = getStore({name: 'currentPeriod'});
      if(currentPeriod!=null){
        return new Promise((resolve, reject) => {
          try{
            resolve(currentPeriod)
          }catch(e){
            reject(e);
          }
        })
      }
      else{
        // 重新更新并获取数据
        return this.$store.dispatch("LoadCurrentPeriod");
      }
    },




    /***
     * 获取存货配置
     * @returns {Promise<unknown>|Promise<any>}
     */
    getStockConfig() {
      let stockConfig = getStore({name: 'stockConfig'});
      if(stockConfig!= undefined && stockConfig.accountMethod){
        return new Promise((resolve, reject) => {
          try{
            resolve(stockConfig)
          }catch(e){
            reject(e);
          }
        })
      }
      else{
        // 重新更新并获取数据
        return this.$store.dispatch("LoadStockConfig");
      }
    },
    /**
     * 调用接口获取存货数据
     * @returns
     */
    getNewStockConfig() {
        // 重新更新并获取数据
        return this.$store.dispatch("LoadStockConfig");
    },





    formatSubjectCode(subjectCode){
      if(!subjectCode) return;
      let subjectCodeRule = this.getCurrentAccountInfo().subjectCodeRule;
      return  formatSubjectCode(subjectCodeRule,subjectCode,'.');
      // if(subjectCode.indexOf('_') >=0){
      //   return  subjectCode;
      // }else {
      //   let subjectCodeRule = this.getCurrentAccountInfo().subjectCodeRule;
      //   return  formatSubjectCode(subjectCodeRule,subjectCode,'.',data);
      // }

    },


    /***
     * 获取 账期列表（结账后 需要更新缓存 调用LoadPeriodList）
     * @returns {Promise<unknown>|Promise<any>}
     */
    getPeriodList() {
      let periodList = getStore({name: 'periodList'});
      if(periodList!=null){
        return new Promise((resolve, reject) => {
          try{
            resolve(periodList)
          }catch(e){
            reject(e);
          }
        })
      }
      else{
        // 重新更新并获取数据
        return this.$store.dispatch("LoadPeriodList");
      }
    },

    /***
     * 获取 所有的账期列表（结账后 需要更新缓存 调用 LoadPeriodAllList）
     * @returns {Promise<unknown>|Promise<any>}
     */
    getPeriodAllList() {
      let periodList = getStore({name: 'periodAllList'});
      if(periodList!=null){
        return new Promise((resolve, reject) => {
          try{
            resolve(periodList)
          }catch(e){
            reject(e);
          }
        })
      }
      else{
        // 重新更新并获取数据
        return this.$store.dispatch("LoadPeriodAllList");
      }
    },

    // 表格根据科目层级显示高亮
    tableRowStyle(row){
      if(row.subjectCode && row.subjectCode.length < 5){
        return {
          // backgroundColor: '#fbbd08'
          // backgroundColor: '#f0f2f5'
          backgroundColor: '#d6dfea'
        }
      }
    },

    // 金额千分位
    levelFilter (value){
      // console.log(value.replace(/￥/g,''));
      value = parseFloat(value)
      if(!isNaN(value) && value != 0){
          if(Number.isInteger(value)){
              value = value.toLocaleString() + '.00'
          }else {
              if(value.toLocaleString().substr(-2,1) == '.'){
                  value = value.toLocaleString() + '0'
              }else {
                  value = value.toLocaleString()
              }
          }
      }else if(value == 0 || isNaN(value)){
        value = null
      }
      return value
    },

    /**
     * 获取审批节点的操作文本
     * @param {*} key 
     * @returns 
     */
    getCheckedNodeText(key){
        switch (key) {
            case 'APPROVE':
                return '同意';
            case 'REJECT':
                return '拒绝';
            case 'TRANSFER':
                return '转交';
            default:
                return '';
        }
    },











}
};
export default commonMixin;
