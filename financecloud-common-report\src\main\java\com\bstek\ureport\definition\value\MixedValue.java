/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.definition.value;

import com.bstek.ureport.expression.ExpressionUtils;
import com.bstek.ureport.expression.model.Expression;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 混合值类型，支持文本和表达式的混合
 * 例如："查询日期：${startDate} 至 ${endDate}"
 * 
 * <AUTHOR> Team
 * @since 2025-01-27
 */
public class MixedValue implements Value {
    private String originalText;
    private List<TextSegment> segments;
    
    public MixedValue(String text) {
        this.originalText = text;
        this.segments = parseTextSegments(text);
    }
    
    @Override
    public ValueType getType() {
        // 使用 expression 类型，因为包含表达式处理
        return ValueType.expression;
    }
    
    @Override
    public String getValue() {
        return originalText;
    }
    
    /**
     * 获取解析后的文本段列表
     */
    public List<TextSegment> getSegments() {
        return segments;
    }


    
    /**
     * 解析文本，分离出纯文本和表达式部分
     */
    private List<TextSegment> parseTextSegments(String text) {
        List<TextSegment> result = new ArrayList<>();
        
        // 使用正则表达式匹配 ${...} 表达式
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(text);
        
        int lastEnd = 0;
        while (matcher.find()) {
            // 添加表达式前的纯文本部分
            if (matcher.start() > lastEnd) {
                String plainText = text.substring(lastEnd, matcher.start());
                result.add(new TextSegment(plainText, false));
            }
            
            // 添加表达式部分
            String expressionText = matcher.group(1); // 不包含 ${}
            result.add(new TextSegment(expressionText, true));
            
            lastEnd = matcher.end();
        }
        
        // 添加最后的纯文本部分
        if (lastEnd < text.length()) {
            String plainText = text.substring(lastEnd);
            result.add(new TextSegment(plainText, false));
        }
        
        return result;
    }
    
    /**
     * 文本段类，表示一段纯文本或表达式
     */
    public static class TextSegment {
        private String text;
        private boolean isExpression;
        private Expression expression;
        
        public TextSegment(String text, boolean isExpression) {
            this.text = text;
            this.isExpression = isExpression;
            
            if (isExpression) {
                try {
                    // 解析表达式
                    this.expression = ExpressionUtils.parseExpression(text);
                } catch (Exception e) {
                    // 如果解析失败，当作纯文本处理
                    this.isExpression = false;
                    this.expression = null;
                }
            }
        }
        
        public String getText() {
            return text;
        }
        
        public boolean isExpression() {
            return isExpression;
        }
        
        public Expression getExpression() {
            return expression;
        }
    }
}
