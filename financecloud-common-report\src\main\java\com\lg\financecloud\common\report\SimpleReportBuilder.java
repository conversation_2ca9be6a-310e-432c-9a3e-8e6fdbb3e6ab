package com.lg.financecloud.common.report;

import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.export.ExportConfigure;
import com.bstek.ureport.export.ExportManager;
import com.bstek.ureport.export.ReportRender;
import com.bstek.ureport.model.Report;
import com.lg.financecloud.common.core.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简化的报表构建器
 * 提供简单易用的编程API来生成各种格式的报表
 * 
 * <AUTHOR> Team
 * @since 2025-08-21
 */
@Slf4j
public class SimpleReportBuilder {
    
    private String templatePath;
    private Map<String, Object> parameters = new HashMap<>();
    
    private final ReportRender reportRender;
    private final ExportManager exportManager;
    
    public SimpleReportBuilder() {
        this.reportRender = SpringContextHolder.getBean(ReportRender.class);
        this.exportManager = SpringContextHolder.getBean(ExportManager.class);
    }
    
    /**
     * 设置报表模板路径
     * @param templatePath 模板路径，支持以下格式：
     *                    - "db:reportCode" - 数据库存储模式
     *                    - "file:path/to/template.ureport" - 文件存储模式
     *                    - "classpath:templates/report.ureport" - 类路径存储模式
     * @return 当前构建器实例
     */
    public SimpleReportBuilder template(String templatePath) {
        if (templatePath == null || templatePath.trim().isEmpty()) {
            throw new IllegalArgumentException("模板路径不能为空");
        }

        // 验证模板路径格式
        if (!templatePath.contains(":")) {
            // 如果没有指定前缀，默认为数据库模式
            templatePath = "db:" + templatePath;
        }

        this.templatePath = templatePath;
        return this;
    }
    
    /**
     * 添加参数
     * @param key 参数名
     * @param value 参数值
     * @return 当前构建器实例
     */
    public SimpleReportBuilder param(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }
    
    /**
     * 批量添加参数
     * @param params 参数映射
     * @return 当前构建器实例
     */
    public SimpleReportBuilder params(Map<String, Object> params) {
        if (params != null) {
            this.parameters.putAll(params);
        }
        return this;
    }

    /**
     * 添加数据集（兼容方法）
     * @param name 数据集名称
     * @param data 数据列表
     * @return 当前构建器实例
     */
    public SimpleReportBuilder dataset(String name, List<Map<String, Object>> data) {
        // 将数据集添加到参数中，供CommonReportDataSourceBean使用
        this.parameters.put(name, data);
        return this;
    }
    

    
    /**
     * 构建报表对象
     * @return 报表对象
     */
    public Report build() {
        try {
            // 获取报表定义
            ReportDefinition reportDefinition = reportRender.getReportDefinition(templatePath);

            // 渲染报表（数据通过CommonReportDataSourceBean统一处理）
            Report report = reportRender.render(reportDefinition, parameters);

            return report;
        } catch (Exception e) {
            log.error("构建报表失败", e);
            throw new RuntimeException("构建报表失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 导出为PDF格式
     * @return 字节数组输出流
     */
    public ByteArrayOutputStream exportToPdf() {
        return exportToStream("pdf");
    }
    
    /**
     * 导出为Excel格式
     * @return 字节数组输出流
     */
    public ByteArrayOutputStream exportToExcel() {
        return exportToStream("excel");
    }
    
    /**
     * 导出为Word格式
     * @return 字节数组输出流
     */
    public ByteArrayOutputStream exportToWord() {
        return exportToStream("word");
    }
    
    /**
     * 导出为HTML格式
     * @return HTML内容字符串
     */
    public String exportToHtml() {
        try {
            return exportManager.exportHtml(templatePath, "", parameters).getContent();
        } catch (Exception e) {
            log.error("导出HTML失败", e);
            throw new RuntimeException("导出HTML失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 导出到指定格式的流
     * @param format 导出格式
     * @return 字节数组输出流
     */
    public ByteArrayOutputStream exportToStream(String format) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            exportToStream(format, outputStream);
            return outputStream;
        } catch (Exception e) {
            log.error("导出{}格式失败", format, e);
            throw new RuntimeException("导出" + format + "格式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 导出到指定输出流
     * @param format 导出格式
     * @param outputStream 输出流
     */
    public void exportToStream(String format, OutputStream outputStream) {
        try {
            ExportConfigure configure = new SimpleExportConfigure(templatePath, parameters, outputStream);

            switch (format.toLowerCase()) {
                case "pdf":
                    exportManager.exportPdf(configure);
                    break;
                case "excel":
                    exportManager.exportExcel(configure);
                    break;
                case "excel97":
                    exportManager.exportExcel97(configure);
                    break;
                case "word":
                    exportManager.exportWord(configure);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的导出格式: " + format);
            }
        } catch (Exception e) {
            log.error("导出{}格式到流失败", format, e);
            throw new RuntimeException("导出" + format + "格式到流失败: " + e.getMessage(), e);
        }
    }
    

    
    /**
     * 创建新的构建器实例
     * @return 新的构建器实例
     */
    public static SimpleReportBuilder create() {
        return new SimpleReportBuilder();
    }
    
    /**
     * 创建带模板的构建器实例
     * @param templatePath 报表模板路径
     * @return 新的构建器实例
     */
    public static SimpleReportBuilder create(String templatePath) {
        System.out.println("🔍 SimpleReportBuilder.create() - Template path: " + templatePath);
        return new SimpleReportBuilder().template(templatePath);
    }
    

    
    /**
     * 简单导出配置实现
     */
    private static class SimpleExportConfigure implements ExportConfigure {
        private final String file;
        private final Map<String, Object> parameters;
        private final OutputStream outputStream;
        
        public SimpleExportConfigure(String file, Map<String, Object> parameters, OutputStream outputStream) {
            this.file = file;
            this.parameters = parameters;
            this.outputStream = outputStream;
        }
        
        @Override
        public String getFile() {
            return file;
        }
        
        @Override
        public Map<String, Object> getParameters() {
            return parameters;
        }
        
        @Override
        public OutputStream getOutputStream() {
            return outputStream;
        }
    }
}
