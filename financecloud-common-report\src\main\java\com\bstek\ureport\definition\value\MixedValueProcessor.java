package com.bstek.ureport.definition.value;

import com.bstek.ureport.build.Context;
import com.bstek.ureport.expression.model.Expression;
import com.bstek.ureport.expression.model.data.ExpressionData;
import com.bstek.ureport.expression.model.data.ObjectExpressionData;
import com.bstek.ureport.model.Cell;

/**
 * MixedValue 处理器，用于处理包含 ${} 参数替换的混合文本
 * 
 * <AUTHOR> Team
 * @since 2025-01-27
 */
public class MixedValueProcessor {
    
    /**
     * 处理 MixedValue，将其中的表达式替换为实际值
     * 
     * @param mixedValue MixedValue 对象
     * @param cell 当前单元格
     * @param context 上下文
     * @return 处理后的字符串
     */
    public static String processMixedValue(MixedValue mixedValue, Cell cell, Context context) {
        if (mixedValue == null || mixedValue.getSegments() == null) {
            return "";
        }
        
        StringBuilder result = new StringBuilder();
        
        for (MixedValue.TextSegment segment : mixedValue.getSegments()) {
            if (segment.isExpression()) {
                // 处理表达式部分
                Expression expression = segment.getExpression();
                if (expression != null) {
                    try {
                        ExpressionData<?> exprData = expression.execute(cell, cell, context);
                        if (exprData instanceof ObjectExpressionData) {
                            Object value = ((ObjectExpressionData) exprData).getData();
                            result.append(value != null ? value.toString() : "");
                        } else {
                            result.append("");
                        }
                    } catch (Exception e) {
                        // 如果表达式执行失败，保留原始文本
                        result.append("${").append(segment.getText()).append("}");
                    }
                } else {
                    // 如果表达式解析失败，保留原始文本
                    result.append("${").append(segment.getText()).append("}");
                }
            } else {
                // 处理纯文本部分
                result.append(segment.getText());
            }
        }
        
        return result.toString();
    }
}
