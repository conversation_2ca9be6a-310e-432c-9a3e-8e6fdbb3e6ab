/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.parser.impl.value;

import org.dom4j.Element;

import com.bstek.ureport.definition.value.SimpleValue;
import com.bstek.ureport.definition.value.Value;
import com.bstek.ureport.definition.value.MixedValue;

/**
 * <AUTHOR>
 * @since 2016年12月21日
 *
 * Enhanced by FinanceCloud Team to support expressions in simple-value nodes
 */
public class SimpleValueParser extends ValueParser {
	@Override
	public Value parse(Element element) {
		String text = element.getText();

		// 🎯 扩展支持：检查是否包含表达式语法 ${...}
		if (text != null && text.contains("${") && text.contains("}")) {
			// 如果包含表达式语法，使用 MixedValue 来处理混合文本和表达式
			return new MixedValue(text);
		} else {
			// 否则使用原来的 SimpleValue
			return new SimpleValue(text);
		}
	}
}
